// pages/index/index.js
const app = getApp()

Page({
  data: {
    loading: true,
    hotEmojis: [],
    categories: [
      { id: 1, name: '搞笑', icon: '/images/funny.png', keyword: '搞笑' },
      { id: 2, name: '可爱', icon: '/images/cute.png', keyword: '可爱' },
      { id: 3, name: '表情', icon: '/images/expression.png', keyword: '表情' },
      { id: 4, name: '动物', icon: '/images/animal.png', keyword: '动物' },
      { id: 5, name: '明星', icon: '/images/star.png', keyword: '明星' },
      { id: 6, name: '影视', icon: '/images/movie.png', keyword: '影视' }
    ],
    recentSearches: []
  },

  onLoad() {
    this.loadHotEmojis()
    this.loadRecentSearches()
  },

  onShow() {
    // 刷新收藏状态
    this.setData({
      hotEmojis: this.data.hotEmojis
    })
    this.loadRecentSearches()
  },

  // 加载热门表情包
  loadHotEmojis() {
    this.setData({ loading: true })
    
    // 模拟API调用
    setTimeout(() => {
      const mockData = [
        {
          id: 1,
          title: '熊猫头表情包',
          thumbnail: 'https://via.placeholder.com/200x200/FFB6C1/000000?text=熊猫头',
          count: 20,
          category: '搞笑',
          tags: ['熊猫', '搞笑', '可爱']
        },
        {
          id: 2,
          title: '沙雕表情包',
          thumbnail: 'https://via.placeholder.com/200x200/98FB98/000000?text=沙雕',
          count: 15,
          category: '搞笑',
          tags: ['沙雕', '搞笑', '日常']
        },
        {
          id: 3,
          title: '猫咪表情包',
          thumbnail: 'https://via.placeholder.com/200x200/87CEEB/000000?text=猫咪',
          count: 25,
          category: '可爱',
          tags: ['猫咪', '可爱', '动物']
        },
        {
          id: 4,
          title: '明星表情包',
          thumbnail: 'https://via.placeholder.com/200x200/DDA0DD/000000?text=明星',
          count: 18,
          category: '明星',
          tags: ['明星', '娱乐', '搞笑']
        }
      ]
      
      this.setData({
        hotEmojis: mockData,
        loading: false
      })
    }, 1000)
  },

  // 加载最近搜索
  loadRecentSearches() {
    app.loadSearchHistory()
    this.setData({
      recentSearches: app.globalData.searchHistory.slice(0, 5)
    })
  },

  // 跳转到搜索页面
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 跳转到分类页面
  goToCategory(e) {
    const category = e.currentTarget.dataset.category
    wx.navigateTo({
      url: `/pages/category/category?keyword=${category.keyword}&title=${category.name}`
    })
  },

  // 跳转到详情页面
  goToDetail(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },

  // 切换收藏状态
  toggleFavorite(e) {
    const emoji = e.currentTarget.dataset.emoji
    const isFavorited = app.isFavorite(emoji.id)
    
    if (isFavorited) {
      app.removeFavorite(emoji.id)
    } else {
      app.addFavorite(emoji)
    }
    
    // 触发页面更新
    this.setData({
      hotEmojis: this.data.hotEmojis
    })
  },

  // 搜索关键词
  searchKeyword(e) {
    const keyword = e.currentTarget.dataset.keyword
    wx.navigateTo({
      url: `/pages/search/search?keyword=${keyword}`
    })
  },

  // 清空搜索历史
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearSearchHistory()
          this.setData({
            recentSearches: []
          })
        }
      }
    })
  },

  // 查看更多
  viewMore() {
    wx.switchTab({
      url: '/pages/category/category'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadHotEmojis()
    wx.stopPullDownRefresh()
  }
})
