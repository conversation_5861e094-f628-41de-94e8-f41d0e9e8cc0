// pages/category/category.js
const app = getApp()

Page({
  data: {
    loading: true,
    loadingMore: false,
    hasMore: true,
    page: 1,
    emojiList: [],
    currentCategory: '',
    currentCategoryName: '',
    currentKeyword: '',
    categories: [
      { id: 0, name: '全部', icon: '/images/all.png', keyword: '' },
      { id: 1, name: '搞笑', icon: '/images/funny.png', keyword: '搞笑' },
      { id: 2, name: '可爱', icon: '/images/cute.png', keyword: '可爱' },
      { id: 3, name: '表情', icon: '/images/expression.png', keyword: '表情' },
      { id: 4, name: '动物', icon: '/images/animal.png', keyword: '动物' },
      { id: 5, name: '明星', icon: '/images/star.png', keyword: '明星' },
      { id: 6, name: '影视', icon: '/images/movie.png', keyword: '影视' }
    ],
    sortType: 'hot',
    sortText: '热度排序',
    showSort: false,
    sortOptions: [
      { label: '热度排序', value: 'hot' },
      { label: '最新发布', value: 'new' },
      { label: '图片最多', value: 'count' },
      { label: '名称排序', value: 'name' }
    ]
  },

  onLoad(options) {
    const { keyword, title } = options
    if (keyword) {
      this.setData({
        currentKeyword: keyword,
        currentCategoryName: title || keyword,
        currentCategory: keyword
      })
    }
    this.loadEmojiList()
  },

  onShow() {
    // 刷新收藏状态
    this.setData({
      emojiList: this.data.emojiList
    })
  },

  // 选择分类
  selectCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentCategory: category.keyword,
      currentCategoryName: category.name,
      currentKeyword: '',
      emojiList: [],
      page: 1,
      hasMore: true
    })
    this.loadEmojiList()
  },

  // 加载表情包列表
  loadEmojiList() {
    this.setData({ loading: true })
    this.fetchEmojiList()
  },

  // 获取表情包数据
  fetchEmojiList() {
    const { currentCategory, currentKeyword, page, sortType } = this.data
    
    // 模拟API调用
    setTimeout(() => {
      const mockData = this.generateMockData(currentCategory || currentKeyword, page, sortType)
      
      const newList = page === 1 ? mockData : [...this.data.emojiList, ...mockData]
      
      this.setData({
        emojiList: newList,
        loading: false,
        loadingMore: false,
        hasMore: mockData.length >= 10 // 假设每页10条
      })
    }, 1000)
  },

  // 生成模拟数据
  generateMockData(category, page, sortType) {
    const results = []
    const startId = (page - 1) * 10 + 1
    const categoryName = category || '表情包'
    
    for (let i = 0; i < 10; i++) {
      const id = startId + i
      const item = {
        id: id,
        title: `${categoryName}${id}`,
        thumbnail: `https://via.placeholder.com/200x200/87CEEB/000000?text=${categoryName}${id}`,
        count: Math.floor(Math.random() * 30) + 5,
        category: categoryName,
        tags: [categoryName, '搞笑', '表情'],
        hot: Math.floor(Math.random() * 1000),
        createTime: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
      }
      results.push(item)
    }
    
    // 根据排序类型排序
    switch (sortType) {
      case 'hot':
        results.sort((a, b) => b.hot - a.hot)
        break
      case 'new':
        results.sort((a, b) => b.createTime - a.createTime)
        break
      case 'count':
        results.sort((a, b) => b.count - a.count)
        break
      case 'name':
        results.sort((a, b) => a.title.localeCompare(b.title))
        break
    }
    
    // 模拟最后一页数据较少
    if (page >= 3) {
      return results.slice(0, Math.floor(Math.random() * 5))
    }
    
    return results
  },

  // 跳转到搜索页
  goToSearch() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 显示排序菜单
  showSortMenu() {
    this.setData({
      showSort: true
    })
  },

  // 隐藏排序菜单
  hideSortMenu() {
    this.setData({
      showSort: false
    })
  },

  // 选择排序方式
  selectSort(e) {
    const sortType = e.currentTarget.dataset.sort
    const sortOption = this.data.sortOptions.find(item => item.value === sortType)
    
    this.setData({
      sortType: sortType,
      sortText: sortOption.label,
      showSort: false,
      emojiList: [],
      page: 1,
      hasMore: true
    })
    
    this.loadEmojiList()
  },

  // 加载更多
  loadMore() {
    if (this.data.loadingMore || !this.data.hasMore) return
    
    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    })
    
    this.fetchEmojiList()
  },

  // 跳转到详情页
  goToDetail(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },

  // 切换收藏
  toggleFavorite(e) {
    const emoji = e.currentTarget.dataset.emoji
    const isFavorited = app.isFavorite(emoji.id)
    
    if (isFavorited) {
      app.removeFavorite(emoji.id)
    } else {
      app.addFavorite(emoji)
    }
    
    // 触发页面更新
    this.setData({
      emojiList: this.data.emojiList
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      emojiList: [],
      page: 1,
      hasMore: true
    })
    this.loadEmojiList()
    wx.stopPullDownRefresh()
  },

  // 触底加载更多
  onReachBottom() {
    this.loadMore()
  }
})
