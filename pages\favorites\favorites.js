// pages/favorites/favorites.js
const app = getApp()

Page({
  data: {
    loading: true,
    favorites: [],
    displayList: [],
    categories: [],
    currentFilter: '',
    totalImages: 0,
    editMode: false,
    selectedItems: [],
    allSelected: false,
    sortType: 'time',
    sortText: '收藏时间',
    showSort: false,
    sortOptions: [
      { label: '收藏时间', value: 'time' },
      { label: '名称排序', value: 'name' },
      { label: '图片数量', value: 'count' },
      { label: '分类排序', value: 'category' }
    ]
  },

  onLoad() {
    this.loadFavorites()
  },

  onShow() {
    this.loadFavorites()
  },

  // 加载收藏列表
  loadFavorites() {
    this.setData({ loading: true })
    
    // 加载收藏数据
    app.loadFavorites()
    const favorites = app.globalData.favorites.map(item => ({
      ...item,
      favoriteTime: this.formatTime(item.favoriteTime || Date.now())
    }))
    
    // 计算统计数据
    const categories = [...new Set(favorites.map(item => item.category))].filter(Boolean)
    const totalImages = favorites.reduce((sum, item) => sum + (item.count || 0), 0)
    
    this.setData({
      favorites: favorites,
      categories: categories,
      totalImages: totalImages,
      loading: false
    })
    
    this.filterAndSort()
  },

  // 格式化时间
  formatTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    
    if (diff < minute) {
      return '刚刚'
    } else if (diff < hour) {
      return `${Math.floor(diff / minute)}分钟前`
    } else if (diff < day) {
      return `${Math.floor(diff / hour)}小时前`
    } else if (diff < 7 * day) {
      return `${Math.floor(diff / day)}天前`
    } else {
      const date = new Date(timestamp)
      return `${date.getMonth() + 1}-${date.getDate()}`
    }
  },

  // 筛选和排序
  filterAndSort() {
    let list = [...this.data.favorites]
    
    // 分类筛选
    if (this.data.currentFilter) {
      list = list.filter(item => item.category === this.data.currentFilter)
    }
    
    // 排序
    const { sortType } = this.data
    switch (sortType) {
      case 'time':
        list.sort((a, b) => (b.favoriteTime || 0) - (a.favoriteTime || 0))
        break
      case 'name':
        list.sort((a, b) => a.title.localeCompare(b.title))
        break
      case 'count':
        list.sort((a, b) => (b.count || 0) - (a.count || 0))
        break
      case 'category':
        list.sort((a, b) => (a.category || '').localeCompare(b.category || ''))
        break
    }
    
    this.setData({
      displayList: list
    })
  },

  // 按分类筛选
  filterByCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({
      currentFilter: category,
      editMode: false,
      selectedItems: [],
      allSelected: false
    })
    this.filterAndSort()
  },

  // 切换编辑模式
  toggleEditMode() {
    this.setData({
      editMode: !this.data.editMode,
      selectedItems: [],
      allSelected: false
    })
  },

  // 切换选择
  toggleSelect(e) {
    const id = e.currentTarget.dataset.id
    const selectedItems = [...this.data.selectedItems]
    const index = selectedItems.indexOf(id)
    
    if (index > -1) {
      selectedItems.splice(index, 1)
    } else {
      selectedItems.push(id)
    }
    
    this.setData({
      selectedItems: selectedItems,
      allSelected: selectedItems.length === this.data.displayList.length
    })
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { allSelected, displayList } = this.data
    
    if (allSelected) {
      this.setData({
        selectedItems: [],
        allSelected: false
      })
    } else {
      this.setData({
        selectedItems: displayList.map(item => item.id),
        allSelected: true
      })
    }
  },

  // 批量删除
  batchDelete() {
    const { selectedItems } = this.data
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedItems.length}个表情包吗？`,
      success: (res) => {
        if (res.confirm) {
          selectedItems.forEach(id => {
            app.removeFavorite(id)
          })
          
          this.setData({
            editMode: false,
            selectedItems: [],
            allSelected: false
          })
          
          this.loadFavorites()
        }
      }
    })
  },

  // 移除收藏
  removeFavorite(e) {
    const id = e.currentTarget.dataset.id
    const item = this.data.favorites.find(fav => fav.id === id)
    
    wx.showModal({
      title: '取消收藏',
      content: `确定要取消收藏"${item.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          app.removeFavorite(id)
          this.loadFavorites()
        }
      }
    })
  },

  // 显示排序菜单
  showSortMenu() {
    this.setData({
      showSort: true
    })
  },

  // 隐藏排序菜单
  hideSortMenu() {
    this.setData({
      showSort: false
    })
  },

  // 选择排序方式
  selectSort(e) {
    const sortType = e.currentTarget.dataset.sort
    const sortOption = this.data.sortOptions.find(item => item.value === sortType)
    
    this.setData({
      sortType: sortType,
      sortText: sortOption.label,
      showSort: false
    })
    
    this.filterAndSort()
  },

  // 跳转到详情页
  goToDetail(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },

  // 跳转到首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadFavorites()
    wx.stopPullDownRefresh()
  },

  // 分享页面
  onShareAppMessage() {
    return {
      title: '我的表情包收藏 - 斗图神器',
      path: '/pages/index/index'
    }
  }
})
