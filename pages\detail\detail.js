// pages/detail/detail.js
const app = getApp()

Page({
  data: {
    loading: true,
    emojiData: null,
    emojiImages: [],
    relatedEmojis: [],
    isFavorited: false,
    viewMode: 'grid' // grid 或 list
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.loadEmojiDetail(id)
    }
  },

  onShow() {
    // 刷新收藏状态
    if (this.data.emojiData) {
      this.setData({
        isFavorited: app.isFavorite(this.data.emojiData.id)
      })
    }
  },

  // 加载表情包详情
  loadEmojiDetail(id) {
    this.setData({ loading: true })
    
    // 模拟API调用
    setTimeout(() => {
      const mockData = {
        id: parseInt(id),
        title: `表情包${id}`,
        count: 20,
        category: '搞笑',
        tags: ['搞笑', '表情', '日常'],
        description: '这是一个很有趣的表情包集合'
      }
      
      const mockImages = []
      for (let i = 1; i <= mockData.count; i++) {
        mockImages.push({
          id: i,
          name: `表情${i}.jpg`,
          url: `https://via.placeholder.com/300x300/87CEEB/000000?text=表情${i}`,
          size: '128KB'
        })
      }
      
      const mockRelated = []
      for (let i = 1; i <= 5; i++) {
        const relatedId = parseInt(id) + i
        mockRelated.push({
          id: relatedId,
          title: `相关表情包${relatedId}`,
          thumbnail: `https://via.placeholder.com/200x200/FFB6C1/000000?text=相关${relatedId}`
        })
      }
      
      this.setData({
        emojiData: mockData,
        emojiImages: mockImages,
        relatedEmojis: mockRelated,
        isFavorited: app.isFavorite(mockData.id),
        loading: false
      })
    }, 1000)
  },

  // 切换收藏状态
  toggleFavorite() {
    const { emojiData, isFavorited } = this.data
    
    if (isFavorited) {
      app.removeFavorite(emojiData.id)
    } else {
      app.addFavorite(emojiData)
    }
    
    this.setData({
      isFavorited: !isFavorited
    })
  },

  // 分享表情包
  shareEmoji() {
    const { emojiData } = this.data
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    wx.showToast({
      title: '点击右上角分享',
      icon: 'none'
    })
  },

  // 下载全部图片
  downloadAll() {
    wx.showModal({
      title: '下载提示',
      content: `确定要下载全部${this.data.emojiImages.length}张图片吗？`,
      success: (res) => {
        if (res.confirm) {
          this.batchDownload()
        }
      }
    })
  },

  // 批量下载
  batchDownload() {
    const images = this.data.emojiImages
    let downloadCount = 0
    
    wx.showLoading({
      title: `下载中 0/${images.length}`
    })
    
    images.forEach((image, index) => {
      wx.downloadFile({
        url: image.url,
        success: (res) => {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              downloadCount++
              wx.showLoading({
                title: `下载中 ${downloadCount}/${images.length}`
              })
              
              if (downloadCount === images.length) {
                wx.hideLoading()
                wx.showToast({
                  title: '全部下载完成',
                  icon: 'success'
                })
              }
            },
            fail: () => {
              downloadCount++
              if (downloadCount === images.length) {
                wx.hideLoading()
                wx.showToast({
                  title: '下载完成',
                  icon: 'success'
                })
              }
            }
          })
        },
        fail: () => {
          downloadCount++
          if (downloadCount === images.length) {
            wx.hideLoading()
            wx.showToast({
              title: '下载完成',
              icon: 'success'
            })
          }
        }
      })
    })
  },

  // 切换视图模式
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      viewMode: mode
    })
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.emojiImages.map(img => img.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  // 下载单张图片
  downloadImage(e) {
    const image = e.currentTarget.dataset.image
    
    wx.downloadFile({
      url: image.url,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      },
      fail: () => {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  },

  // 分享单张图片
  shareImage(e) {
    const image = e.currentTarget.dataset.image
    wx.showToast({
      title: '长按图片分享',
      icon: 'none'
    })
  },

  // 跳转到其他详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.redirectTo({
      url: `/pages/detail/detail?id=${id}`
    })
  },

  // 分享配置
  onShareAppMessage() {
    const { emojiData } = this.data
    return {
      title: `分享表情包：${emojiData.title}`,
      path: `/pages/detail/detail?id=${emojiData.id}`,
      imageUrl: emojiData.thumbnail
    }
  },

  onShareTimeline() {
    const { emojiData } = this.data
    return {
      title: `${emojiData.title} - 斗图神器`,
      query: `id=${emojiData.id}`,
      imageUrl: emojiData.thumbnail
    }
  }
})
