<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box" bindtap="goToSearch">
      <image class="search-icon" src="/images/search.png" mode="aspectFit"></image>
      <text class="search-placeholder">搜索表情包...</text>
    </view>
  </view>

  <!-- 热门分类 -->
  <view class="category-section">
    <view class="section-title">热门分类</view>
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view 
          class="category-item" 
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="goToCategory"
          data-category="{{item}}"
        >
          <image class="category-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="category-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 热门表情包 -->
  <view class="hot-section">
    <view class="section-title">
      <text>热门表情包</text>
      <text class="more-btn" bindtap="viewMore">更多</text>
    </view>
    
    <view class="emoji-grid" wx:if="{{hotEmojis.length > 0}}">
      <view 
        class="emoji-item" 
        wx:for="{{hotEmojis}}" 
        wx:key="id"
        bindtap="goToDetail"
        data-emoji="{{item}}"
      >
        <image class="emoji-image" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <view class="emoji-meta">
            <text class="emoji-count">{{item.count}}张</text>
            <view class="emoji-actions">
              <image 
                class="action-icon {{app.isFavorite(item.id) ? 'favorited' : ''}}" 
                src="/images/heart.png" 
                bindtap="toggleFavorite"
                data-emoji="{{item}}"
                catchtap="true"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{loading}}">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && hotEmojis.length === 0}}">
      <image class="empty-icon" src="/images/empty.png"></image>
      <text class="empty-text">暂无表情包数据</text>
    </view>
  </view>

  <!-- 最近搜索 -->
  <view class="recent-section" wx:if="{{recentSearches.length > 0}}">
    <view class="section-title">
      <text>最近搜索</text>
      <text class="clear-btn" bindtap="clearHistory">清空</text>
    </view>
    <view class="recent-tags">
      <text 
        class="tag" 
        wx:for="{{recentSearches}}" 
        wx:key="*this"
        bindtap="searchKeyword"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>
</view>
