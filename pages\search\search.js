// pages/search/search.js
const app = getApp()

Page({
  data: {
    keyword: '',
    autoFocus: true,
    hasSearched: false,
    searching: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    searchResults: [],
    searchHistory: [],
    suggestions: ['熊猫头', '沙雕', '猫咪', '表情包', '搞笑', '可爱', '明星', '影视']
  },

  onLoad(options) {
    // 如果有传入关键词，直接搜索
    if (options.keyword) {
      this.setData({
        keyword: options.keyword,
        autoFocus: false
      })
      this.performSearch()
    }
    this.loadSearchHistory()
  },

  onShow() {
    this.loadSearchHistory()
  },

  // 加载搜索历史
  loadSearchHistory() {
    app.loadSearchHistory()
    this.setData({
      searchHistory: app.globalData.searchHistory
    })
  },

  // 输入事件
  onInput(e) {
    this.setData({
      keyword: e.detail.value
    })
  },

  // 搜索
  onSearch() {
    const keyword = this.data.keyword.trim()
    if (!keyword) {
      wx.showToast({
        title: '请输入搜索关键词',
        icon: 'none'
      })
      return
    }
    
    // 添加到搜索历史
    app.addSearchHistory(keyword)
    this.loadSearchHistory()
    
    this.performSearch()
  },

  // 执行搜索
  performSearch() {
    this.setData({
      hasSearched: true,
      searching: true,
      searchResults: [],
      page: 1,
      hasMore: true
    })

    this.searchEmojis()
  },

  // 搜索表情包
  searchEmojis() {
    const { keyword, page } = this.data
    
    // 模拟API调用
    setTimeout(() => {
      const mockResults = this.generateMockResults(keyword, page)
      
      const newResults = page === 1 ? mockResults : [...this.data.searchResults, ...mockResults]
      
      this.setData({
        searchResults: newResults,
        searching: false,
        loadingMore: false,
        hasMore: mockResults.length >= 10 // 假设每页10条
      })
    }, 1000)
  },

  // 生成模拟数据
  generateMockResults(keyword, page) {
    const results = []
    const startId = (page - 1) * 10 + 1
    
    for (let i = 0; i < 10; i++) {
      const id = startId + i
      results.push({
        id: id,
        title: `${keyword}表情包${id}`,
        thumbnail: `https://via.placeholder.com/200x200/87CEEB/000000?text=${keyword}${id}`,
        count: Math.floor(Math.random() * 30) + 5,
        category: '搞笑',
        tags: [keyword, '搞笑', '表情']
      })
    }
    
    // 模拟最后一页数据较少
    if (page >= 3) {
      return results.slice(0, Math.floor(Math.random() * 5))
    }
    
    return results
  },

  // 搜索建议
  searchSuggestion(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      keyword: keyword
    })
    this.onSearch()
  },

  // 搜索历史
  searchHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    this.setData({
      keyword: keyword
    })
    this.onSearch()
  },

  // 删除历史记录
  deleteHistory(e) {
    const keyword = e.currentTarget.dataset.keyword
    const history = this.data.searchHistory.filter(item => item !== keyword)
    app.globalData.searchHistory = history
    app.saveSearchHistory()
    this.setData({
      searchHistory: history
    })
  },

  // 清空历史
  clearHistory() {
    wx.showModal({
      title: '提示',
      content: '确定要清空搜索历史吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearSearchHistory()
          this.setData({
            searchHistory: []
          })
        }
      }
    })
  },

  // 加载更多
  loadMore() {
    if (this.data.loadingMore || !this.data.hasMore) return
    
    this.setData({
      loadingMore: true,
      page: this.data.page + 1
    })
    
    this.searchEmojis()
  },

  // 跳转到详情页
  goToDetail(e) {
    const emoji = e.currentTarget.dataset.emoji
    wx.navigateTo({
      url: `/pages/detail/detail?id=${emoji.id}`
    })
  },

  // 切换收藏
  toggleFavorite(e) {
    const emoji = e.currentTarget.dataset.emoji
    const isFavorited = app.isFavorite(emoji.id)
    
    if (isFavorited) {
      app.removeFavorite(emoji.id)
    } else {
      app.addFavorite(emoji)
    }
    
    // 触发页面更新
    this.setData({
      searchResults: this.data.searchResults
    })
  },

  // 返回
  goBack() {
    wx.navigateBack()
  },

  // 触底加载更多
  onReachBottom() {
    this.loadMore()
  }
})
