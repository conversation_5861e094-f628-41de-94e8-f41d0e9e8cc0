/* pages/favorites/favorites.wxss */
.container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 头部统计 */
.header-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  padding: 40rpx 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  color: white;
}

.stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.stats-divider {
  width: 1rpx;
  background: rgba(255, 255, 255, 0.3);
  margin: 10rpx 0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.edit-btn {
  color: #667eea;
  font-size: 28rpx;
  padding: 15rpx 30rpx;
  border: 2rpx solid #667eea;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.edit-btn.active {
  background: #667eea;
  color: white;
}

.sort-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #667eea;
}

.sort-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
}

/* 分类筛选 */
.category-filter {
  margin: 0 20rpx 20rpx;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: inline-flex;
  padding: 10rpx 0;
}

.filter-item {
  background: white;
  color: #666;
  padding: 15rpx 30rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin-right: 20rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-item.active {
  background: #667eea;
  color: white;
}

/* 收藏列表 */
.favorites-list {
  margin: 0 20rpx;
}

/* 全选操作 */
.select-all {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.select-all-btn {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.batch-actions {
  display: flex;
  gap: 20rpx;
}

.batch-btn {
  background: #ff4757;
  color: white;
  padding: 15rpx 25rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
}

/* 表情包网格 */
.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.emoji-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.emoji-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.emoji-item.edit-mode {
  transform: none;
}

.emoji-item.edit-mode:active {
  transform: scale(0.98);
}

/* 选择框 */
.select-box {
  position: absolute;
  top: 15rpx;
  left: 15rpx;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 5rpx;
}

.emoji-image {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
}

.emoji-info {
  padding: 20rpx;
}

.emoji-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.emoji-count {
  font-size: 24rpx;
  color: #999;
}

.emoji-category {
  background: #f0f2ff;
  color: #667eea;
  padding: 6rpx 15rpx;
  border-radius: 15rpx;
  font-size: 22rpx;
}

.emoji-time {
  margin-top: 10rpx;
}

.time-text {
  font-size: 22rpx;
  color: #999;
}

/* 快速操作 */
.quick-actions {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  padding: 10rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.emoji-item:active .quick-actions {
  opacity: 1;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  filter: brightness(0) invert(1);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 150rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  opacity: 0.3;
}

.empty-text {
  color: #333;
  font-size: 32rpx;
  margin-bottom: 15rpx;
}

.empty-tip {
  color: #999;
  font-size: 26rpx;
  margin-bottom: 40rpx;
}

.goto-home-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 排序菜单 */
.sort-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sort-menu.show {
  opacity: 1;
  visibility: visible;
}

.sort-content {
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  padding: 40rpx 30rpx;
  width: 100%;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.sort-menu.show .sort-content {
  transform: translateY(0);
}

.sort-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #667eea;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}
