# 斗图神器 - 微信小程序

一个功能完整的斗图表情包微信小程序，支持表情包搜索、分类浏览、收藏管理等功能。

## 功能特性

### 🏠 首页
- 热门表情包展示
- 分类快速导航
- 搜索功能入口
- 最近搜索历史

### 🔍 搜索功能
- 关键词搜索表情包
- 搜索建议和历史
- 搜索结果分页加载
- 支持收藏和分享

### 📂 分类浏览
- 多种分类筛选
- 排序功能（热度、时间、数量等）
- 无限滚动加载
- 网格布局展示

### ❤️ 收藏管理
- 表情包收藏/取消收藏
- 收藏列表管理
- 批量操作（删除、选择）
- 分类筛选和排序

### 📱 表情包详情
- 高清图片预览
- 批量下载功能
- 分享到微信
- 相关推荐

## 技术栈

- **框架**: 微信小程序原生开发
- **样式**: WXSS + Flexbox 布局
- **数据存储**: 微信小程序本地存储
- **UI设计**: 现代化渐变设计风格

## 项目结构

```
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── sitemap.json          # 站点地图配置
├── images/               # 图标资源文件夹
│   └── README.md         # 图标说明文件
└── pages/                # 页面文件夹
    ├── index/            # 首页
    │   ├── index.js
    │   ├── index.json
    │   ├── index.wxml
    │   └── index.wxss
    ├── search/           # 搜索页
    │   ├── search.js
    │   ├── search.json
    │   ├── search.wxml
    │   └── search.wxss
    ├── detail/           # 详情页
    │   ├── detail.js
    │   ├── detail.json
    │   ├── detail.wxml
    │   └── detail.wxss
    ├── category/         # 分类页
    │   ├── category.js
    │   ├── category.json
    │   ├── category.wxml
    │   └── category.wxss
    └── favorites/        # 收藏页
        ├── favorites.js
        ├── favorites.json
        ├── favorites.wxml
        └── favorites.wxss
```

## 快速开始

### 1. 环境准备
- 安装微信开发者工具
- 注册微信小程序账号
- 获取小程序 AppID

### 2. 项目配置
1. 在微信开发者工具中导入项目
2. 配置 AppID
3. 添加图标文件到 `images/` 文件夹
4. 配置服务器域名（如需要）

### 3. 图标准备
根据 `images/README.md` 文件说明，准备所需的图标文件。

### 4. API 接口
当前使用模拟数据，实际使用时需要：
1. 替换模拟数据为真实 API 调用
2. 配置服务器域名白名单
3. 处理网络请求错误

## 主要功能模块

### 全局数据管理 (app.js)
- 用户信息管理
- 收藏数据管理
- 搜索历史管理
- 本地存储操作

### 首页模块 (pages/index/)
- 热门表情包展示
- 分类导航
- 搜索入口
- 下拉刷新

### 搜索模块 (pages/search/)
- 实时搜索
- 搜索历史
- 结果分页
- 无限滚动

### 详情模块 (pages/detail/)
- 图片预览
- 批量下载
- 分享功能
- 相关推荐

### 分类模块 (pages/category/)
- 分类筛选
- 排序功能
- 网格布局
- 触底加载

### 收藏模块 (pages/favorites/)
- 收藏管理
- 批量操作
- 分类筛选
- 编辑模式

## 样式特色

- 🎨 现代化渐变设计
- 📱 响应式布局
- ✨ 流畅的动画效果
- 🎯 直观的交互设计
- 🌈 统一的视觉风格

## 开发注意事项

1. **图片资源**: 需要准备完整的图标文件
2. **API 接口**: 替换模拟数据为真实接口
3. **权限申请**: 下载功能需要申请相册权限
4. **性能优化**: 图片懒加载和列表虚拟化
5. **错误处理**: 网络异常和数据异常处理

## 扩展功能建议

- [ ] 用户登录和同步
- [ ] 表情包上传功能
- [ ] 社交分享优化
- [ ] 离线缓存机制
- [ ] 个性化推荐
- [ ] 表情包制作工具

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
