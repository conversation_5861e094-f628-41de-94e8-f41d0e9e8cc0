/* pages/category/category.wxss */
.container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 分类导航 */
.category-nav {
  background: white;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.nav-scroll {
  white-space: nowrap;
}

.nav-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.nav-item.active {
  background: #f0f2ff;
}

.nav-item:active {
  transform: scale(0.95);
}

.nav-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.nav-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

.nav-item.active .nav-name {
  color: #667eea;
  font-weight: 500;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx;
}

.search-box {
  background: white;
  border-radius: 50rpx;
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.search-text {
  color: #999;
  font-size: 28rpx;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.result-count {
  font-size: 26rpx;
  color: #999;
}

.sort-btn {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #667eea;
}

.sort-icon {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10rpx;
  transition: transform 0.3s ease;
}

/* 表情包列表 */
.emoji-list {
  display: flex;
  flex-wrap: wrap;
  padding: 0 20rpx;
  margin: -10rpx;
}

.emoji-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.emoji-image {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
}

.emoji-info {
  padding: 20rpx;
}

.emoji-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.emoji-count {
  font-size: 24rpx;
  color: #999;
}

.emoji-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.action-icon.favorited {
  opacity: 1;
  filter: hue-rotate(320deg) saturate(2);
}

.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  margin: -2rpx;
}

.tag-small {
  background: #f0f2ff;
  color: #667eea;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin: 2rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-btn {
  color: #667eea;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: 2rpx solid #667eea;
  border-radius: 50rpx;
  display: inline-block;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #667eea;
  color: white;
}

/* 加载和空状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  color: #333;
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.empty-tip {
  color: #999;
  font-size: 26rpx;
}

/* 排序菜单 */
.sort-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.sort-menu.show {
  opacity: 1;
  visibility: visible;
}

.sort-content {
  background: white;
  border-radius: 30rpx 30rpx 0 0;
  padding: 40rpx 30rpx;
  width: 100%;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.sort-menu.show .sort-content {
  transform: translateY(0);
}

.sort-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.sort-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 28rpx;
  color: #333;
}

.sort-option:last-child {
  border-bottom: none;
}

.sort-option.active {
  color: #667eea;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}
