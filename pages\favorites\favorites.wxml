<!--pages/favorites/favorites.wxml-->
<view class="container">
  <!-- 头部统计 -->
  <view class="header-stats" wx:if="{{favorites.length > 0}}">
    <view class="stats-item">
      <text class="stats-number">{{favorites.length}}</text>
      <text class="stats-label">已收藏</text>
    </view>
    <view class="stats-divider"></view>
    <view class="stats-item">
      <text class="stats-number">{{totalImages}}</text>
      <text class="stats-label">总图片</text>
    </view>
    <view class="stats-divider"></view>
    <view class="stats-item">
      <text class="stats-number">{{categories.length}}</text>
      <text class="stats-label">分类</text>
    </view>
  </view>

  <!-- 操作栏 -->
  <view class="action-bar" wx:if="{{favorites.length > 0}}">
    <view class="action-left">
      <text class="edit-btn {{editMode ? 'active' : ''}}" bindtap="toggleEditMode">
        {{editMode ? '完成' : '编辑'}}
      </text>
    </view>
    <view class="action-right">
      <view class="sort-btn" bindtap="showSortMenu">
        <text>{{sortText}}</text>
        <image class="sort-icon" src="/images/arrow-down.png"></image>
      </view>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter" wx:if="{{favorites.length > 0 && categories.length > 1}}">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <text 
          class="filter-item {{currentFilter === '' ? 'active' : ''}}" 
          bindtap="filterByCategory"
          data-category=""
        >全部</text>
        <text 
          class="filter-item {{currentFilter === item ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="*this"
          bindtap="filterByCategory"
          data-category="{{item}}"
        >{{item}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 收藏列表 -->
  <view class="favorites-list" wx:if="{{displayList.length > 0}}">
    <!-- 全选操作 -->
    <view class="select-all" wx:if="{{editMode}}">
      <view class="select-all-btn" bindtap="toggleSelectAll">
        <image class="checkbox" src="/images/{{allSelected ? 'checked' : 'unchecked'}}.png"></image>
        <text>全选</text>
      </view>
      <view class="batch-actions" wx:if="{{selectedItems.length > 0}}">
        <text class="batch-btn" bindtap="batchDelete">删除({{selectedItems.length}})</text>
      </view>
    </view>

    <!-- 表情包列表 -->
    <view class="emoji-grid">
      <view 
        class="emoji-item {{editMode ? 'edit-mode' : ''}}" 
        wx:for="{{displayList}}" 
        wx:key="id"
        bindtap="{{editMode ? 'toggleSelect' : 'goToDetail'}}"
        data-emoji="{{item}}"
        data-id="{{item.id}}"
      >
        <!-- 选择框 -->
        <view class="select-box" wx:if="{{editMode}}">
          <image 
            class="checkbox" 
            src="/images/{{selectedItems.indexOf(item.id) > -1 ? 'checked' : 'unchecked'}}.png"
          ></image>
        </view>

        <image class="emoji-image" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
        <view class="emoji-info">
          <text class="emoji-title">{{item.title}}</text>
          <view class="emoji-meta">
            <text class="emoji-count">{{item.count}}张</text>
            <text class="emoji-category">{{item.category}}</text>
          </view>
          <view class="emoji-time">
            <text class="time-text">{{item.favoriteTime}}</text>
          </view>
        </view>

        <!-- 快速操作 -->
        <view class="quick-actions" wx:if="{{!editMode}}">
          <image 
            class="action-icon" 
            src="/images/delete.png"
            bindtap="removeFavorite"
            data-id="{{item.id}}"
            catchtap="true"
          ></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && favorites.length === 0}}">
    <image class="empty-icon" src="/images/empty-heart.png"></image>
    <text class="empty-text">还没有收藏任何表情包</text>
    <text class="empty-tip">去首页看看有什么好玩的吧</text>
    <button class="goto-home-btn" bindtap="goToHome">去首页逛逛</button>
  </view>

  <!-- 筛选后无结果 -->
  <view class="empty-state" wx:if="{{!loading && favorites.length > 0 && displayList.length === 0}}">
    <image class="empty-icon" src="/images/empty.png"></image>
    <text class="empty-text">该分类下暂无收藏</text>
    <text class="empty-tip">试试其他分类吧</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 排序菜单 -->
  <view class="sort-menu {{showSort ? 'show' : ''}}" bindtap="hideSortMenu">
    <view class="sort-content" catchtap="true">
      <view class="sort-title">排序方式</view>
      <view 
        class="sort-option {{sortType === item.value ? 'active' : ''}}" 
        wx:for="{{sortOptions}}" 
        wx:key="value"
        bindtap="selectSort"
        data-sort="{{item.value}}"
      >
        <text>{{item.label}}</text>
        <image class="check-icon" src="/images/check.png" wx:if="{{sortType === item.value}}"></image>
      </view>
    </view>
  </view>
</view>
