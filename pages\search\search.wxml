<!--pages/search/search.wxml-->
<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-box">
      <image class="search-icon" src="/images/search.png"></image>
      <input 
        class="search-input" 
        placeholder="搜索表情包..." 
        value="{{keyword}}"
        bindinput="onInput"
        bindconfirm="onSearch"
        focus="{{autoFocus}}"
        confirm-type="search"
      />
      <text class="search-btn" bindtap="onSearch" wx:if="{{keyword}}">搜索</text>
    </view>
    <text class="cancel-btn" bindtap="goBack">取消</text>
  </view>

  <!-- 搜索建议 -->
  <view class="suggestions" wx:if="{{!hasSearched && suggestions.length > 0}}">
    <view class="section-title">热门搜索</view>
    <view class="suggestion-tags">
      <text 
        class="tag" 
        wx:for="{{suggestions}}" 
        wx:key="*this"
        bindtap="searchSuggestion"
        data-keyword="{{item}}"
      >{{item}}</text>
    </view>
  </view>

  <!-- 搜索历史 -->
  <view class="history" wx:if="{{!hasSearched && searchHistory.length > 0}}">
    <view class="section-title">
      <text>搜索历史</text>
      <text class="clear-btn" bindtap="clearHistory">清空</text>
    </view>
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{searchHistory}}" 
        wx:key="*this"
        bindtap="searchHistory"
        data-keyword="{{item}}"
      >
        <image class="history-icon" src="/images/history.png"></image>
        <text class="history-text">{{item}}</text>
        <image 
          class="delete-icon" 
          src="/images/delete.png"
          bindtap="deleteHistory"
          data-keyword="{{item}}"
          catchtap="true"
        ></image>
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="results" wx:if="{{hasSearched}}">
    <view class="result-header" wx:if="{{searchResults.length > 0}}">
      <text>找到 {{searchResults.length}} 个结果</text>
    </view>

    <!-- 结果列表 -->
    <view class="result-grid" wx:if="{{searchResults.length > 0}}">
      <view 
        class="result-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="goToDetail"
        data-emoji="{{item}}"
      >
        <image class="result-image" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
        <view class="result-info">
          <text class="result-title">{{item.title}}</text>
          <view class="result-meta">
            <text class="result-count">{{item.count}}张</text>
            <view class="result-actions">
              <image 
                class="action-icon {{app.isFavorite(item.id) ? 'favorited' : ''}}" 
                src="/images/heart.png" 
                bindtap="toggleFavorite"
                data-emoji="{{item}}"
                catchtap="true"
              ></image>
            </view>
          </view>
          <view class="result-tags">
            <text 
              class="tag-small" 
              wx:for="{{item.tags}}" 
              wx:for-item="tag" 
              wx:key="*this"
            >{{tag}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore && searchResults.length > 0}}">
      <view class="loading" wx:if="{{loadingMore}}">
        <image class="loading-icon" src="/images/loading.gif"></image>
        <text class="loading-text">加载中...</text>
      </view>
      <text class="load-more-btn" bindtap="loadMore" wx:else>加载更多</text>
    </view>

    <!-- 搜索中 -->
    <view class="loading" wx:if="{{searching}}">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">搜索中...</text>
    </view>

    <!-- 无结果 -->
    <view class="empty-state" wx:if="{{!searching && searchResults.length === 0}}">
      <image class="empty-icon" src="/images/empty.png"></image>
      <text class="empty-text">没有找到相关表情包</text>
      <text class="empty-tip">试试其他关键词吧</text>
    </view>
  </view>
</view>
