/* pages/index/index.wxss */
.container {
  padding-bottom: 20rpx;
}

/* 搜索区域 */
.search-section {
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.search-box {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 25rpx 30rpx;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10rpx);
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.search-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 分类区域 */
.category-section {
  background: white;
  margin-top: 20rpx;
  padding: 30rpx 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 0 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-btn, .clear-btn {
  font-size: 26rpx;
  color: #667eea;
  font-weight: normal;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  background: #f8f9ff;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.category-item:active {
  transform: scale(0.95);
  background: #e8ecff;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}

/* 热门表情包区域 */
.hot-section {
  background: white;
  margin-top: 20rpx;
  padding: 30rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.emoji-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.emoji-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.emoji-image {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
}

.emoji-info {
  padding: 20rpx;
}

.emoji-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emoji-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.emoji-count {
  font-size: 24rpx;
  color: #999;
}

.emoji-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.action-icon.favorited {
  opacity: 1;
  filter: hue-rotate(320deg) saturate(2);
}

/* 最近搜索区域 */
.recent-section {
  background: white;
  margin-top: 20rpx;
  padding: 30rpx;
}

.recent-tags {
  display: flex;
  flex-wrap: wrap;
  margin: -5rpx;
}

.tag {
  background: #f0f2ff;
  color: #667eea;
  padding: 15rpx 25rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin: 5rpx;
  transition: all 0.3s ease;
}

.tag:active {
  background: #667eea;
  color: white;
}

/* 加载和空状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  text-align: center;
}
