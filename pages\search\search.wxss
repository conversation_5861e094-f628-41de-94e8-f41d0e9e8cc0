/* pages/search/search.wxss */
.container {
  height: 100vh;
  background: #f8f8f8;
}

/* 搜索头部 */
.search-header {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-bottom: 1rpx solid #eee;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  color: #667eea;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.cancel-btn {
  color: #667eea;
  font-size: 28rpx;
}

/* 搜索建议 */
.suggestions, .history {
  background: white;
  margin-top: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clear-btn {
  font-size: 26rpx;
  color: #667eea;
  font-weight: normal;
}

.suggestion-tags {
  display: flex;
  flex-wrap: wrap;
  margin: -5rpx;
}

.tag {
  background: #f0f2ff;
  color: #667eea;
  padding: 15rpx 25rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  margin: 5rpx;
  transition: all 0.3s ease;
}

.tag:active {
  background: #667eea;
  color: white;
}

/* 搜索历史 */
.history-list {
  margin: -10rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 20rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.history-item:last-child {
  border-bottom: none;
}

.history-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 20rpx;
  opacity: 0.5;
}

.history-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.delete-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.3;
}

/* 搜索结果 */
.results {
  padding: 20rpx;
}

.result-header {
  background: white;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.result-grid {
  display: flex;
  flex-wrap: wrap;
  margin: -10rpx;
}

.result-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.result-item:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.12);
}

.result-image {
  width: 100%;
  height: 200rpx;
  background: #f5f5f5;
}

.result-info {
  padding: 20rpx;
}

.result-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-count {
  font-size: 24rpx;
  color: #999;
}

.result-actions {
  display: flex;
  align-items: center;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.action-icon.favorited {
  opacity: 1;
  filter: hue-rotate(320deg) saturate(2);
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
  margin: -2rpx;
}

.tag-small {
  background: #f0f2ff;
  color: #667eea;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin: 2rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx;
}

.load-more-btn {
  color: #667eea;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: 2rpx solid #667eea;
  border-radius: 50rpx;
  display: inline-block;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #667eea;
  color: white;
}

/* 加载和空状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  color: #333;
  font-size: 32rpx;
  margin-bottom: 10rpx;
}

.empty-tip {
  color: #999;
  font-size: 26rpx;
}
