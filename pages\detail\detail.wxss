/* pages/detail/detail.wxss */
.container {
  background: #f8f8f8;
  min-height: 100vh;
}

/* 表情包信息 */
.emoji-info {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.info-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.emoji-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.emoji-meta {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.emoji-count {
  font-size: 26rpx;
  color: #999;
  margin-right: 30rpx;
}

.emoji-category {
  background: #f0f2ff;
  color: #667eea;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.emoji-tags {
  display: flex;
  flex-wrap: wrap;
  margin: -5rpx;
}

.tag {
  background: #f5f5f5;
  color: #666;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin: 5rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9ff;
  border: none;
  border-radius: 15rpx;
  padding: 25rpx 10rpx;
  margin: 0 10rpx;
  font-size: 24rpx;
  color: #667eea;
  transition: all 0.3s ease;
}

.action-btn:first-child {
  margin-left: 0;
}

.action-btn:last-child {
  margin-right: 0;
}

.action-btn:active {
  transform: scale(0.95);
  background: #e8ecff;
}

.favorite-btn.favorited {
  background: #ffe8ec;
  color: #ff4757;
}

.btn-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

/* 图片列表 */
.emoji-list {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.list-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-mode {
  display: flex;
  background: #f5f5f5;
  border-radius: 25rpx;
  overflow: hidden;
}

.mode-btn {
  padding: 12rpx 25rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #667eea;
  color: white;
}

/* 网格视图 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  margin: -10rpx;
}

.image-item {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  position: relative;
  border-radius: 15rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.emoji-image {
  width: 100%;
  height: 200rpx;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.image-item:active .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 30rpx;
}

.action-icon {
  width: 50rpx;
  height: 50rpx;
  filter: brightness(0) invert(1);
}

/* 列表视图 */
.image-list {
  padding: 20rpx 30rpx;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.list-item:last-child {
  border-bottom: none;
}

.list-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
}

.list-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.image-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.image-size {
  font-size: 24rpx;
  color: #999;
}

.list-actions {
  display: flex;
  gap: 20rpx;
}

.list-actions .action-icon {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  filter: none;
}

/* 相关推荐 */
.related-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx 0;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 0 30rpx 20rpx;
}

.related-scroll {
  white-space: nowrap;
}

.related-list {
  display: inline-flex;
  padding: 0 20rpx;
}

.related-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15rpx;
  width: 200rpx;
}

.related-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 15rpx;
  margin-bottom: 15rpx;
  background: #f5f5f5;
}

.related-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

/* 加载和空状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 150rpx 40rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  text-align: center;
}
