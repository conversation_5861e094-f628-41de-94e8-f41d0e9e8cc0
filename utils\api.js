// utils/api.js

// API 配置
const API_CONFIG = {
  // 开发环境
  development: {
    baseUrl: 'https://dev-api.example.com',
    timeout: 10000
  },
  // 生产环境
  production: {
    baseUrl: 'https://api.example.com',
    timeout: 10000
  }
}

// 当前环境
const ENV = 'development' // 可以根据需要切换

// 获取当前配置
const getCurrentConfig = () => {
  return API_CONFIG[ENV]
}

/**
 * 网络请求封装
 * @param {Object} options 请求参数
 */
const request = (options) => {
  const config = getCurrentConfig()
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: config.baseUrl + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      timeout: options.timeout || config.timeout,
      success: (res) => {
        if (res.statusCode === 200) {
          // 根据实际 API 响应格式调整
          if (res.data.code === 0) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '网络请求失败'))
      }
    })
  })
}

/**
 * GET 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他选项
 */
const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST 请求
 * @param {string} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他选项
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

// API 接口定义
const API = {
  // 表情包相关接口
  emoji: {
    // 获取热门表情包
    getHotEmojis: (params) => get('/emoji/hot', params),
    
    // 搜索表情包
    searchEmojis: (params) => get('/emoji/search', params),
    
    // 获取表情包详情
    getEmojiDetail: (id) => get(`/emoji/detail/${id}`),
    
    // 获取表情包图片列表
    getEmojiImages: (id) => get(`/emoji/${id}/images`),
    
    // 获取分类表情包
    getCategoryEmojis: (params) => get('/emoji/category', params),
    
    // 获取相关推荐
    getRelatedEmojis: (id) => get(`/emoji/${id}/related`)
  },
  
  // 分类相关接口
  category: {
    // 获取所有分类
    getCategories: () => get('/categories'),
    
    // 获取分类详情
    getCategoryDetail: (id) => get(`/category/${id}`)
  },
  
  // 用户相关接口
  user: {
    // 用户登录
    login: (data) => post('/user/login', data),
    
    // 获取用户信息
    getUserInfo: () => get('/user/info'),
    
    // 同步收藏
    syncFavorites: (data) => post('/user/favorites/sync', data),
    
    // 获取收藏列表
    getFavorites: () => get('/user/favorites')
  }
}

// 模拟数据生成器（开发阶段使用）
const mockData = {
  // 生成热门表情包数据
  generateHotEmojis: () => {
    const emojis = []
    for (let i = 1; i <= 20; i++) {
      emojis.push({
        id: i,
        title: `热门表情包${i}`,
        thumbnail: `https://via.placeholder.com/200x200/87CEEB/000000?text=热门${i}`,
        count: Math.floor(Math.random() * 30) + 5,
        category: ['搞笑', '可爱', '表情', '动物'][Math.floor(Math.random() * 4)],
        tags: ['搞笑', '表情', '日常'],
        hot: Math.floor(Math.random() * 1000)
      })
    }
    return emojis
  },
  
  // 生成搜索结果数据
  generateSearchResults: (keyword, page = 1) => {
    const results = []
    const startId = (page - 1) * 10 + 1
    
    for (let i = 0; i < 10; i++) {
      const id = startId + i
      results.push({
        id: id,
        title: `${keyword}表情包${id}`,
        thumbnail: `https://via.placeholder.com/200x200/87CEEB/000000?text=${keyword}${id}`,
        count: Math.floor(Math.random() * 30) + 5,
        category: keyword,
        tags: [keyword, '搞笑', '表情']
      })
    }
    
    return results
  },
  
  // 生成表情包详情数据
  generateEmojiDetail: (id) => {
    return {
      id: parseInt(id),
      title: `表情包${id}`,
      count: 20,
      category: '搞笑',
      tags: ['搞笑', '表情', '日常'],
      description: '这是一个很有趣的表情包集合',
      images: Array.from({ length: 20 }, (_, i) => ({
        id: i + 1,
        name: `表情${i + 1}.jpg`,
        url: `https://via.placeholder.com/300x300/87CEEB/000000?text=表情${i + 1}`,
        size: '128KB'
      }))
    }
  }
}

// 模拟 API 调用（开发阶段使用）
const mockAPI = {
  emoji: {
    getHotEmojis: () => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockData.generateHotEmojis())
        }, 1000)
      })
    },
    
    searchEmojis: (params) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockData.generateSearchResults(params.keyword, params.page))
        }, 1000)
      })
    },
    
    getEmojiDetail: (id) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockData.generateEmojiDetail(id))
        }, 1000)
      })
    }
  }
}

// 导出 API（开发阶段使用 mockAPI，生产阶段使用 API）
const exportAPI = ENV === 'development' ? mockAPI : API

module.exports = {
  request,
  get,
  post,
  API: exportAPI,
  mockData
}
