<!--pages/category/category.wxml-->
<view class="container">
  <!-- 分类导航 -->
  <view class="category-nav" wx:if="{{!currentKeyword}}">
    <scroll-view class="nav-scroll" scroll-x="true">
      <view class="nav-list">
        <view 
          class="nav-item {{currentCategory === item.keyword ? 'active' : ''}}" 
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="selectCategory"
          data-category="{{item}}"
        >
          <image class="nav-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="nav-name">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box" bindtap="goToSearch">
      <image class="search-icon" src="/images/search.png"></image>
      <text class="search-text">{{currentKeyword || '搜索' + (currentCategoryName || '表情包') + '...'}}</text>
    </view>
  </view>

  <!-- 排序和筛选 -->
  <view class="filter-section">
    <view class="filter-left">
      <text class="result-count" wx:if="{{emojiList.length > 0}}">共{{emojiList.length}}个表情包</text>
    </view>
    <view class="filter-right">
      <view class="sort-btn" bindtap="showSortMenu">
        <text>{{sortText}}</text>
        <image class="sort-icon" src="/images/arrow-down.png"></image>
      </view>
    </view>
  </view>

  <!-- 表情包列表 -->
  <view class="emoji-list" wx:if="{{emojiList.length > 0}}">
    <view 
      class="emoji-item" 
      wx:for="{{emojiList}}" 
      wx:key="id"
      bindtap="goToDetail"
      data-emoji="{{item}}"
    >
      <image class="emoji-image" src="{{item.thumbnail}}" mode="aspectFill" lazy-load="true"></image>
      <view class="emoji-info">
        <text class="emoji-title">{{item.title}}</text>
        <view class="emoji-meta">
          <text class="emoji-count">{{item.count}}张</text>
          <view class="emoji-actions">
            <image 
              class="action-icon {{app.isFavorite(item.id) ? 'favorited' : ''}}" 
              src="/images/heart.png" 
              bindtap="toggleFavorite"
              data-emoji="{{item}}"
              catchtap="true"
            ></image>
          </view>
        </view>
        <view class="emoji-tags">
          <text 
            class="tag-small" 
            wx:for="{{item.tags}}" 
            wx:for-item="tag" 
            wx:key="*this"
          >{{tag}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{hasMore && emojiList.length > 0}}">
    <view class="loading" wx:if="{{loadingMore}}">
      <image class="loading-icon" src="/images/loading.gif"></image>
      <text class="loading-text">加载中...</text>
    </view>
    <text class="load-more-btn" bindtap="loadMore" wx:else>加载更多</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && emojiList.length === 0}}">
    <image class="empty-icon" src="/images/empty.png"></image>
    <text class="empty-text">暂无{{currentCategoryName || '表情包'}}数据</text>
    <text class="empty-tip">试试其他分类吧</text>
  </view>

  <!-- 排序菜单 -->
  <view class="sort-menu {{showSort ? 'show' : ''}}" bindtap="hideSortMenu">
    <view class="sort-content" catchtap="true">
      <view class="sort-title">排序方式</view>
      <view 
        class="sort-option {{sortType === item.value ? 'active' : ''}}" 
        wx:for="{{sortOptions}}" 
        wx:key="value"
        bindtap="selectSort"
        data-sort="{{item.value}}"
      >
        <text>{{item.label}}</text>
        <image class="check-icon" src="/images/check.png" wx:if="{{sortType === item.value}}"></image>
      </view>
    </view>
  </view>
</view>
