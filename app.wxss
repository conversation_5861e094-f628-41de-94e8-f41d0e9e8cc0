/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 通用按钮样式 */
.btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 15rpx 0 rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx 0 rgba(102, 126, 234, 0.3);
}

.btn-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.btn-success {
  background: linear-gradient(45deg, #56ab2f 0%, #a8e6cf 100%);
}

.btn-danger {
  background: linear-gradient(45deg, #ff416c 0%, #ff4b2b 100%);
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

/* 搜索框样式 */
.search-box {
  background: white;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 加载动画 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
}

.loading-text {
  margin-left: 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  text-align: center;
  line-height: 1.5;
}

/* 网格布局 */
.grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.grid-item {
  width: calc(50% - 20rpx);
  margin: 10rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  background: #f0f0f0;
  color: #666;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin: 5rpx;
}

.tag-active {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
  animation: slideInDown 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.rotate {
  animation: rotate 1s linear infinite;
}

/* 过渡效果 */
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

/* 阴影效果 */
.shadow-sm {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.shadow-md {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.shadow-lg {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.2);
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(45deg, #56ab2f 0%, #a8e6cf 100%);
}

.gradient-warning {
  background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
}

.gradient-info {
  background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
}

/* 文本效果 */
.text-gradient {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 毛玻璃效果 */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 响应式工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

/* 圆角工具类 */
.rounded-sm { border-radius: 10rpx; }
.rounded { border-radius: 15rpx; }
.rounded-lg { border-radius: 20rpx; }
.rounded-xl { border-radius: 30rpx; }
.rounded-full { border-radius: 50%; }
