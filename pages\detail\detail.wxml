<!--pages/detail/detail.wxml-->
<view class="container">
  <!-- 表情包信息 -->
  <view class="emoji-info" wx:if="{{emojiData}}">
    <view class="info-header">
      <text class="emoji-title">{{emojiData.title}}</text>
      <view class="emoji-meta">
        <text class="emoji-count">{{emojiData.count}}张图片</text>
        <text class="emoji-category">{{emojiData.category}}</text>
      </view>
      <view class="emoji-tags">
        <text class="tag" wx:for="{{emojiData.tags}}" wx:key="*this">{{item}}</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button 
        class="action-btn favorite-btn {{isFavorited ? 'favorited' : ''}}" 
        bindtap="toggleFavorite"
      >
        <image class="btn-icon" src="/images/heart.png"></image>
        <text>{{isFavorited ? '已收藏' : '收藏'}}</text>
      </button>
      <button class="action-btn share-btn" bindtap="shareEmoji">
        <image class="btn-icon" src="/images/share.png"></image>
        <text>分享</text>
      </button>
      <button class="action-btn download-btn" bindtap="downloadAll">
        <image class="btn-icon" src="/images/download.png"></image>
        <text>下载全部</text>
      </button>
    </view>
  </view>

  <!-- 表情包图片列表 -->
  <view class="emoji-list" wx:if="{{emojiImages.length > 0}}">
    <view class="list-header">
      <text class="list-title">表情包图片</text>
      <view class="view-mode">
        <text 
          class="mode-btn {{viewMode === 'grid' ? 'active' : ''}}" 
          bindtap="switchViewMode"
          data-mode="grid"
        >网格</text>
        <text 
          class="mode-btn {{viewMode === 'list' ? 'active' : ''}}" 
          bindtap="switchViewMode"
          data-mode="list"
        >列表</text>
      </view>
    </view>

    <!-- 网格视图 -->
    <view class="image-grid" wx:if="{{viewMode === 'grid'}}">
      <view 
        class="image-item" 
        wx:for="{{emojiImages}}" 
        wx:key="id"
        bindtap="previewImage"
        data-index="{{index}}"
      >
        <image class="emoji-image" src="{{item.url}}" mode="aspectFill" lazy-load="true"></image>
        <view class="image-overlay">
          <view class="image-actions">
            <image 
              class="action-icon" 
              src="/images/download.png"
              bindtap="downloadImage"
              data-image="{{item}}"
              catchtap="true"
            ></image>
            <image 
              class="action-icon" 
              src="/images/share.png"
              bindtap="shareImage"
              data-image="{{item}}"
              catchtap="true"
            ></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 列表视图 -->
    <view class="image-list" wx:if="{{viewMode === 'list'}}">
      <view 
        class="list-item" 
        wx:for="{{emojiImages}}" 
        wx:key="id"
        bindtap="previewImage"
        data-index="{{index}}"
      >
        <image class="list-image" src="{{item.url}}" mode="aspectFill" lazy-load="true"></image>
        <view class="list-info">
          <text class="image-name">{{item.name}}</text>
          <text class="image-size">{{item.size}}</text>
        </view>
        <view class="list-actions">
          <image 
            class="action-icon" 
            src="/images/download.png"
            bindtap="downloadImage"
            data-image="{{item}}"
            catchtap="true"
          ></image>
          <image 
            class="action-icon" 
            src="/images/share.png"
            bindtap="shareImage"
            data-image="{{item}}"
            catchtap="true"
          ></image>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedEmojis.length > 0}}">
    <view class="section-title">相关推荐</view>
    <scroll-view class="related-scroll" scroll-x="true">
      <view class="related-list">
        <view 
          class="related-item" 
          wx:for="{{relatedEmojis}}" 
          wx:key="id"
          bindtap="goToDetail"
          data-id="{{item.id}}"
        >
          <image class="related-image" src="{{item.thumbnail}}" mode="aspectFill"></image>
          <text class="related-title">{{item.title}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <image class="loading-icon" src="/images/loading.gif"></image>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && !emojiData}}">
    <image class="empty-icon" src="/images/empty.png"></image>
    <text class="empty-text">表情包不存在或已删除</text>
  </view>
</view>
