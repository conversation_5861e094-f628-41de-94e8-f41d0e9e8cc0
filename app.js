// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 初始化数据
    this.loadFavorites()
    this.loadSearchHistory()

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  
  globalData: {
    userInfo: null,
    favorites: [], // 收藏的表情包
    searchHistory: [] // 搜索历史
  },

  // 添加收藏
  addFavorite(item) {
    const favorites = this.globalData.favorites
    const exists = favorites.find(fav => fav.id === item.id)
    if (!exists) {
      favorites.unshift(item)
      this.saveFavorites()
      wx.showToast({
        title: '收藏成功',
        icon: 'success'
      })
      return true
    } else {
      wx.showToast({
        title: '已收藏过了',
        icon: 'none'
      })
      return false
    }
  },

  // 移除收藏
  removeFavorite(id) {
    const favorites = this.globalData.favorites
    const index = favorites.findIndex(fav => fav.id === id)
    if (index > -1) {
      favorites.splice(index, 1)
      this.saveFavorites()
      wx.showToast({
        title: '取消收藏',
        icon: 'success'
      })
      return true
    }
    return false
  },

  // 检查是否已收藏
  isFavorite(id) {
    return this.globalData.favorites.some(fav => fav.id === id)
  },

  // 保存收藏到本地存储
  saveFavorites() {
    wx.setStorageSync('favorites', this.globalData.favorites)
  },

  // 加载收藏
  loadFavorites() {
    const favorites = wx.getStorageSync('favorites') || []
    this.globalData.favorites = favorites
  },

  // 添加搜索历史
  addSearchHistory(keyword) {
    const history = this.globalData.searchHistory
    const index = history.indexOf(keyword)
    if (index > -1) {
      history.splice(index, 1)
    }
    history.unshift(keyword)
    // 最多保存10条搜索历史
    if (history.length > 10) {
      history.pop()
    }
    this.saveSearchHistory()
  },

  // 保存搜索历史
  saveSearchHistory() {
    wx.setStorageSync('searchHistory', this.globalData.searchHistory)
  },

  // 加载搜索历史
  loadSearchHistory() {
    const history = wx.getStorageSync('searchHistory') || []
    this.globalData.searchHistory = history
  },

  // 清空搜索历史
  clearSearchHistory() {
    this.globalData.searchHistory = []
    wx.removeStorageSync('searchHistory')
  }
})
